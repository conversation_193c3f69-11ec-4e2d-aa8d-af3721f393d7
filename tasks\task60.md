TASK-1:
look what we implemented in task59.md - DungeonGroundTrap (cs, tscn). Now, we will be implementing a moving trap. It will work as follow:
1) we have MovingDungeonTrap.tscn. It has animation player with animation Animate. This animation is looped. Play it at _ready. Trap will have in sprite2d->Area2D. Handle this area like in DungeonGroundTrap and as i described in task59.md - proper player detection, layer etc. This trap is active all the time, not like task59.md where it was hidden at start.
2) i will add this trap in other scenes and as child i will add 2 Node2D: Left and Right. That you need to get poins from them that informs about max left and max right position of the trap. Trap will move from Left to Right and back. When it reaches Left or Right then it should change direction. Trap will move at constant speed. When it reaches Left or Right then it should wait 0.5s then change direction. When area is entered then player should take damage to player. Let me choos how many damage it takes in inspector.

TASK-2:
When DungeonGroundTrap hits player then player has this a bit red background. But it should be visible for 0.7s and then restore to normal. Same for MovingDungeonTrap.

TASK-3:
Make sure the project builds.

TASK-4:
Read again all required files to make sure you didn't miss anything.