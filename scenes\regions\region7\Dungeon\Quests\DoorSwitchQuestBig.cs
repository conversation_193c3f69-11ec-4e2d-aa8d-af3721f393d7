using Godot;
using System.Collections.Generic;
using System.Linq;

public partial class DoorSwitchQuestBig : Node2D
{
	private const string QuestName = "DoorSwitchQuestBig";
	private readonly int[] _correctOrder = { 1, 5, 4, 2, 3 };
	
	private Dictionary<int, Sprite2D> _switches = new Dictionary<int, Sprite2D>();
	private Dictionary<int, AnimationPlayer> _switchAnimationPlayers = new Dictionary<int, AnimationPlayer>();
	private Dictionary<int, Area2D> _switchAreas = new Dictionary<int, Area2D>();
	private Dictionary<int, bool> _playerInSwitchArea = new Dictionary<int, bool>();
	
	private Sprite2D _door;
	private AnimationPlayer _doorAnimationPlayer;
	
	private List<int> _activatedSwitches = new List<int>();
	private bool _questCompleted = false;

	public override void _Ready()
	{
		InitializeSwitches();
		InitializeDoor();
		
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.EKeyPressed += OnEKeyPressed;
		}
		
		LoadQuestState();
		GD.Print($"DoorSwitchQuestBig: Initialized - quest completed: {_questCompleted}");
	}

	private void InitializeSwitches()
	{
		for (int i = 1; i <= 5; i++)
		{
			var switchNode = GetNode<Sprite2D>($"Switch{i}");
			var animationPlayer = switchNode.GetNode<AnimationPlayer>("AnimationPlayer");
			var area2D = switchNode.GetNode<Area2D>("Area2D");
			
			_switches[i] = switchNode;
			_switchAnimationPlayers[i] = animationPlayer;
			_switchAreas[i] = area2D;
			_playerInSwitchArea[i] = false;
			
			area2D.CollisionMask = 4;

			int switchIndex = i;
			area2D.AreaEntered += (area) => OnSwitchAreaEntered(area, switchIndex);
			area2D.AreaExited += (area) => OnSwitchAreaExited(area, switchIndex);
			
			animationPlayer.Play("Off");
		}
	}

	private void InitializeDoor()
	{
		_door = GetNode<Sprite2D>("Door");
		_doorAnimationPlayer = _door.GetNode<AnimationPlayer>("AnimationPlayer");
	}

	private void OnSwitchAreaEntered(Area2D area, int switchNumber)
	{
		if (area.Name == "PlayerDetector")
		{
			var parent = area.GetParent();
			if (parent is DungeonPlayerController)
			{
				_playerInSwitchArea[switchNumber] = true;
				CommonSignals.Instance?.EmitShowKeyEPrompt();
				GD.Print($"DoorSwitchQuestBig: Player entered switch {switchNumber} area");
			}
		}
	}

	private void OnSwitchAreaExited(Area2D area, int switchNumber)
	{
		if (area.Name == "PlayerDetector")
		{
			var parent = area.GetParent();
			if (parent is DungeonPlayerController)
			{
				_playerInSwitchArea[switchNumber] = false;
				
				bool anyPlayerInArea = _playerInSwitchArea.Values.Any(inArea => inArea);
				if (!anyPlayerInArea)
				{
					CommonSignals.Instance?.EmitHideKeyEPrompt();
				}
				
				GD.Print($"DoorSwitchQuestBig: Player left switch {switchNumber} area");
			}
		}
	}

	private void OnEKeyPressed()
	{
		if (_questCompleted) return;
		
		for (int i = 1; i <= 5; i++)
		{
			if (_playerInSwitchArea[i])
			{
				HandleSwitchPressed(i);
				break;
			}
		}
	}

	private void HandleSwitchPressed(int switchNumber)
	{
		int expectedSwitch = _correctOrder[_activatedSwitches.Count];
		
		if (switchNumber == expectedSwitch)
		{
			ActivateSwitch(switchNumber);
			_activatedSwitches.Add(switchNumber);
			
			GD.Print($"DoorSwitchQuestBig: Correct switch {switchNumber} pressed. Progress: {_activatedSwitches.Count}/5");
			
			if (_activatedSwitches.Count == _correctOrder.Length)
			{
				CompleteQuest();
			}
		}
		else
		{
			GD.Print($"DoorSwitchQuestBig: Wrong switch {switchNumber} pressed. Expected {expectedSwitch}. Resetting all switches.");
			ResetAllSwitches();
		}
		
		SaveQuestState();
	}

	private void ActivateSwitch(int switchNumber)
	{
		if (_switchAnimationPlayers.ContainsKey(switchNumber))
		{
			_switchAnimationPlayers[switchNumber].Play("On");
		}
	}

	private void ResetAllSwitches()
	{
		_activatedSwitches.Clear();
		
		foreach (var kvp in _switchAnimationPlayers)
		{
			kvp.Value.Play("Off");
		}
	}

	private void CompleteQuest()
	{
		_questCompleted = true;
		_doorAnimationPlayer.Play("Open");
		
		GD.Print("DoorSwitchQuestBig: Quest completed! Door opened.");
		SaveQuestState();
	}

	private void LoadQuestState()
	{
		var questData = GameSaveData.Instance.DungeonData.Switches
			.FirstOrDefault(s => s.SwitchName == QuestName);

		if (questData != null && questData.IsOpen)
		{
			_questCompleted = true;
			
			for (int i = 0; i < _correctOrder.Length; i++)
			{
				int switchNumber = _correctOrder[i];
				ActivateSwitch(switchNumber);
				_activatedSwitches.Add(switchNumber);
			}
			
			_doorAnimationPlayer.Play("Open");
			GD.Print("DoorSwitchQuestBig: Loaded completed quest state");
		}
		else
		{
			ResetAllSwitches();
			GD.Print("DoorSwitchQuestBig: Loaded initial quest state");
		}
	}

	private void SaveQuestState()
	{
		var questData = GameSaveData.Instance.DungeonData.Switches
			.FirstOrDefault(s => s.SwitchName == QuestName);

		if (questData != null)
		{
			questData.IsOpen = _questCompleted;
		}
		else
		{
			GameSaveData.Instance.DungeonData.Switches.Add(new DungeonSwitchData
			{
				SwitchName = QuestName,
				IsOpen = _questCompleted
			});
		}

		GD.Print($"DoorSwitchQuestBig: Saved quest state - completed: {_questCompleted}");
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.EKeyPressed -= OnEKeyPressed;
		}

		base._ExitTree();
	}
}
