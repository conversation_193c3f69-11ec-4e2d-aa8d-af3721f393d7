using Godot;

public partial class BagpackButton : Sprite2D
{
	public override void _Ready()
	{
		var button = GetNode<Button>("Button");
		button.Pressed += OnButtonPressed;
	}

	private void OnButtonPressed()
	{
		MenuManager.Instance?.ToggleMenu("InventoryMenu");
	}
	
	public override void _ExitTree()
	{
		var button = GetNode<Button>("Button");
		if (button != null)
		{
			button.Pressed -= OnButtonPressed;
		}
	}
}
