using Godot;
using System;

public partial class DungeonVase : Node2D
{
	[Export] public int MaxHealth { get; set; } = 10;
	[Export] public AudioStream VaseDestroyedAudio { get; set; }
	[Export] public AudioStream VaseHit { get; set; }

	[Signal] public delegate void DungeonVaseDestroyedEventHandler(Vector2I tilePosition, DungeonObjectType objectType);

	private int _currentHealth;
	private Vector2I _tilePosition;
	private bool _isBeingDestroyed = false;
	private Sprite2D _sprite;
	private Tween _hitTween;
	private ProgressBar _hpBar;
	private EffectPlayer _effectPlayer;

	public override void _Ready()
	{
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_hpBar = GetNode<ProgressBar>("ProgressBar");
		_effectPlayer = GetNode<EffectPlayer>("EffectPlayer");

		if (_sprite == null || _hpBar == null || _effectPlayer == null)
		{
			GD.PrintErr("DungeonVase: Required child nodes not found!");
			return;
		}

		// Initially hide HP bar
		_hpBar.Visible = false;

		// Connect to pickaxe signal
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
		}

		GD.Print($"DungeonVase: Ready at position {GlobalPosition}");
	}

	public void Initialize(Vector2I tilePosition, int currentHealth, int maxHealth)
	{
		_tilePosition = tilePosition;
		_currentHealth = currentHealth;
		MaxHealth = maxHealth;

		UpdateHPBar();

		GD.Print($"DungeonVase: Initialized at tile {_tilePosition} with {_currentHealth}/{MaxHealth} HP");
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		GD.Print($"DungeonVase: Pickaxe signal received at tile {tilePosition}, vase at {_tilePosition}");
		if (_tilePosition == tilePosition && !_isBeingDestroyed)
		{
			Vector2I playerTile = GetPlayerTilePosition();
			GD.Print($"DungeonVase: Player at tile {playerTile}, can hit: {CanBeHitFrom(playerTile)}");
			if (CanBeHitFrom(playerTile))
			{
				TakeDamage(damage);
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		// Find player in dungeon scene - it should be at /root/Dungeon/Player
		var player = GetNode<Node2D>("/root/Dungeon/Player");
		if (player != null)
		{
			return new Vector2I(
				Mathf.FloorToInt(player.GlobalPosition.X / 16),
				Mathf.FloorToInt(player.GlobalPosition.Y / 16)
			);
		}
		return Vector2I.Zero;
	}

	private bool CanBeHitFrom(Vector2I playerTile)
	{
		Vector2I distance = _tilePosition - playerTile;
		return Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1;
	}

	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;
		_currentHealth = Mathf.Max(0, _currentHealth);

		// Update health in DungeonManager
		var dungeonManager = GetNode<DungeonManager>("/root/Dungeon/DungeonManager");
		dungeonManager?.UpdateObjectHealth(_tilePosition, _currentHealth);

		UpdateHPBar();

		// Hit animation
		PlayHitAnimation();

		// Play hit sound
		if (VaseHit != null && _effectPlayer != null)
		{
			_effectPlayer.Play(VaseHit);
		}

		GD.Print($"DungeonVase: Took {damage} damage, health now {_currentHealth}/{MaxHealth}");

		if (_currentHealth <= 0)
		{
			DestroyVase();
		}
	}

	private void PlayHitAnimation()
	{
		if (_hitTween != null)
		{
			_hitTween.Kill();
		}

		_hitTween = CreateTween();
		_hitTween.TweenProperty(_sprite, "modulate", new Color(1.0f, 0.42f, 0.27f), 0.1);
		_hitTween.TweenProperty(_sprite, "modulate", Colors.White, 0.1);
	}

	private void UpdateHPBar()
	{
		if (_hpBar == null) return;

		float healthPercentage = (float)_currentHealth / MaxHealth;
		
		if (_currentHealth >= MaxHealth)
		{
			_hpBar.Visible = false;
		}
		else
		{
			_hpBar.Visible = true;
			_hpBar.SetProgress(healthPercentage);
		}
	}

	private void DestroyVase()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		GD.Print($"DungeonVase: Destroying vase at {_tilePosition}");

		// Drop 5 gold coins
		DropGoldCoins();

		// Play destroy sound
		if (VaseDestroyedAudio != null && _effectPlayer != null)
		{
			_effectPlayer.Play(VaseDestroyedAudio);
		}

		// Give XP reward
		CommonSignals.Instance?.EmitAddXp(5);

		// Emit destruction signal
		EmitSignal(SignalName.DungeonVaseDestroyed, _tilePosition, (int)DungeonObjectType.DungeonVase);

		// Remove from scene after short delay
		GetTree().CreateTimer(0.3f).Timeout += () => QueueFree();
	}

	private void DropGoldCoins()
	{
		// Drop 5 gold coins using the existing DroppedResource system
		for (int i = 0; i < 5; i++)
		{
			Vector2 dropPosition = GlobalPosition + new Vector2(
				GD.RandRange(-8, 8),
				GD.RandRange(-8, 8)
			);

			DroppedResource.SpawnCoin(dropPosition, 1);
		}

		GD.Print("DungeonVase: Dropped 5 gold coins");
	}

	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	public Vector2I GetTilePosition()
	{
		return _tilePosition;
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
		}

		_hitTween?.Kill();
		base._ExitTree();
	}
}
