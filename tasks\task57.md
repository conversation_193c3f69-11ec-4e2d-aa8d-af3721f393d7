TASK-1:
In Dungeon.tscn I added NavigationRegion2D. I want to create an enemy - a bat. This enemy will be in dungeon and can move in the region that this navigation region 2d provides. I want you to create bat enemy in following way: 
* I want to be able in DungeonManager to specify where this rat will be spawned. I will tell coordinates but not x,y of the map but of the tile - so we have tiles 0,0, 0,1 etc. Then I will set a bool if this enemy will be spawned every time player enters dungeon or only once - if player kills him then he is dead and should not respawn when i re-enter dungeon.
* <PERSON> has 4 animations: Up,Down,Left,Right (it has only one animation for being idle or moving). Also, I will add 4 animations that indicates that enemy was hit by player - we will have GotHitUp, GotHitDown, GotHitLeft, GotHitRight. 
* Bat should have area2d with circle - when player is in range of this area2d then bat can follow/attack player. When player is out of range then bat should stop and return to it's original spawn location. Bat can move from time to time in range of max 32 pixels from his spawn location (let's wait 3-5s and then move bat to random point in that range but not outside of it). This range does not apply if bat is following player - then he can move further (!).
* Bat can attack player - when bat is close to player (let's say 16 pixels) then bat should attack player. Bat should stop moving and play attack animation (which is: fast move towards player location, like by 6 pixels and return to previous location). Bat should damage player by 1 hp (should be adjustable in inspector). Bat has 5hp. Bat should have progress bar (health bar) that is visible when bat hp is below 100%. When player re-enters cave, bats should be spawned in given locations. Add sprite2d to bat and I will set it and add AnimationPlayer with empty animations - I will add them.
* Make sure bat does not fly directly to player's location to attack - it is not necessairy, bat should fly to player and stay like 16px from him, and let's say max attack range is 18px. Bat should fly to player's location then wait 0.5s then attack then timeout 1s before he can attack again. Bat should follow player if player tries to escape but only if player is inside that area2d range detection. Detect player by the same layer that player uses on it's PlayerDetector (area2d). look at dungeon player and find out which layer is used. And to detect use AreaEntered and AreaExited events.
* Make sure bat can attack dungeon player.
* When i mention player in task-1 i ment for dungeon player and dungeon player controller, do not edit other players scripts - only this from dungeon.
* When bat is killed - he should drop 1 gold coin (dropped resource).
* You can add first bat to dungeon manager at position (tile, which is 16x16) - to tile  (23,-20)
* Player (dungeon player), should be able to attack bat with sword or bow - same way as it works when player attacks rabbit or MeleeEnemy then bat should take damage and play this animation GotHitUp, GotHitDown, GotHitLeft, GotHitRight - depending on which direcion bat was.
* Bat should be called DungeonBat
* You can take some inspirations from MeleeGoblin - BUT DO NOT COPY IT AND DO NOT CREATE SUCH COMPLICATED SYSTEM LIKE INTERFACES OF BASE ENEMY, DO NOT CREATE AREA AND TERITORIAL VARIANTS - here we want to have simplier system but fully functional.
* Please, make this bat system like in AAA game, with very good quality. I will set sprite2d texture and add animations.
* When you finish - make sure project builds
* When you finish and project builds - verify once again if all i told you is implemented correctly
* We will lose a lot of money if this doesn't work so it's crucial you make analysis deeply to make it working.