TASK-1
I added DoorSwitchQuestBig.tscn - read it. It has 5 switches - they all have animation player with On and Off animation. Initailly play Off animation on all switches. We also have door with animation. Initially door is closed. When player clicks on all switches in correct order then door should open. To open door play Open animation. You need to preserve state of this quest - that means if it's completed or not complete. If completed then on init open door and make switches on. So player need to click switches in order: swith 1, 5, 4, 2, 3 -> if player clicks incorrect button in order then you should turn off all switches. Player can't turn switch On when it's off - he can only turn on. So when player enters range of switch then he should be able to click E to turn switch on. Look at which dungeon player listens on player detector - check tscn and cs. And make same for all switches here - listen on proper layer. When player enters range - show KeyE prompt and when leaves - hide.