using Godot;

public partial class DungeonDroppedResourceManager : Node
{
	private bool _hasLoadedResources = false;

	public override void _Ready()
	{
		// Subscribe to game loaded event to spawn saved dropped resources
		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.GameLoaded += OnGameLoaded;
			
			// Also load immediately if ResourcesManager is already initialized
			CallDeferred(nameof(LoadDroppedResources));
		}
	}

	private void OnGameLoaded(object sender, System.EventArgs e)
	{
		LoadDroppedResources();
	}

	private void LoadDroppedResources()
	{
		if (_hasLoadedResources) return;

		var droppedResources = GameSaveData.Instance.DungeonData.DroppedResources;
		if (droppedResources == null || droppedResources.Count == 0) return;

		GD.Print($"DungeonDroppedResourceManager: Loading {droppedResources.Count} dropped resources");

		foreach (var droppedResourceData in droppedResources)
		{
			SpawnDroppedResourceFromData(droppedResourceData);
		}

		_hasLoadedResources = true;
	}

	private void SpawnDroppedResourceFromData(DroppedResourceData data)
	{
		// Load the DroppedResource scene
		var droppedResourceScene = GD.Load<PackedScene>("res://scenes/mapObjects/DroppedResource.tscn");
		if (droppedResourceScene == null)
		{
			GD.PrintErr("DungeonDroppedResourceManager: Could not load DroppedResource.tscn");
			return;
		}

		// Create instance
		var droppedResource = droppedResourceScene.Instantiate<DroppedResource>();
		if (droppedResource == null)
		{
			GD.PrintErr("DungeonDroppedResourceManager: Failed to instantiate DroppedResource");
			return;
		}

		// Set properties from save data
		var position = new Vector2(data.X, data.Y);
		droppedResource.GlobalPosition = position;
		droppedResource.ResourceType = data.ResourceType;
		droppedResource.Quantity = data.Quantity;
		droppedResource.CoinAmount = data.CoinAmount;
		droppedResource.Id = data.Id;

		// Add to scene (find the main dungeon scene)
		var dungeonScene = GetTree().CurrentScene;
		if (dungeonScene != null)
		{
			dungeonScene.AddChild(droppedResource);
		}
		else
		{
			// Fallback: add to this node's parent
			GetParent()?.AddChild(droppedResource);
		}

		// Note: We don't register with ResourcesManager again since it's already in save data
	}

	public override void _ExitTree()
	{
		// Unsubscribe from events
		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.GameLoaded -= OnGameLoaded;
		}
	}
}
