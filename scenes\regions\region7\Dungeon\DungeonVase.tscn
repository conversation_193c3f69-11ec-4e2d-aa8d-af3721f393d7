[gd_scene load_steps=6 format=3 uid="uid://b8vfv1kexku0y"]

[ext_resource type="Script" uid="uid://ck2u702whgj48" path="res://scenes/regions/region7/Dungeon/DungeonVase.cs" id="1_dungeonvase_script"]
[ext_resource type="Texture2D" uid="uid://c4t6437yec22w" path="res://resources/solaria/exterior/dungeonVase.png" id="2_23676"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="2_progressbar"]
[ext_resource type="PackedScene" uid="uid://brwmy8gpv2x3p" path="res://scenes/Audio/EffectPlayer.tscn" id="3_effectplayer"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_23676"]
size = Vector2(10, 6)

[node name="DungeonVase" type="Node2D" groups=["dungeon_objects"]]
y_sort_enabled = true
script = ExtResource("1_dungeonvase_script")

[node name="Sprite2D" type="Sprite2D" parent="."]
y_sort_enabled = true
texture = ExtResource("2_23676")

[node name="ProgressBar" parent="." instance=ExtResource("2_progressbar")]
position = Vector2(0, 7)
scale = Vector2(1, 0.4375)

[node name="EffectPlayer" parent="." instance=ExtResource("3_effectplayer")]

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 3)
shape = SubResource("RectangleShape2D_23676")
