[gd_scene load_steps=12 format=3 uid="uid://b8n7q5mec077b"]

[ext_resource type="Texture2D" uid="uid://b7sfyi050ihmh" path="res://resources/solaria/UI/upgrade_tesla_panel.png" id="1_0dex4"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="2_0dex4"]
[ext_resource type="Texture2D" uid="uid://cbu6enlouh1yc" path="res://resources/solaria/UI/inventory/inventory_item_description.png" id="2_yh1ir"]
[ext_resource type="Texture2D" uid="uid://bgpd0wfpx3kvj" path="res://resources/solaria/UI/inventory/inventory_item_single_slot.png" id="3_5rj7k"]
[ext_resource type="Texture2D" uid="uid://ykix176e2s7a" path="res://resources/solaria/planting/seedbag_resources/beetroot_seedbag.png" id="4_1ux4u"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="5_4vpph"]
[ext_resource type="Texture2D" uid="uid://b4qihgrhky4rk" path="res://resources/solaria/UI/build/button1.png" id="6_1pbao"]

[sub_resource type="Animation" id="Animation_5rj7k"]
resource_name = "Open"
length = 0.2
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CanvasLayer/Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.12, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.8, 0.8), Vector2(1.2, 1.2), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer/Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_1ux4u"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CanvasLayer/Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer/Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_4vpph"]
resource_name = "Close"
length = 0.2
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CanvasLayer/Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.06, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.2, 1.2), Vector2(0.8, 0.8)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer/Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.2),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1pbao"]
_data = {
&"Close": SubResource("Animation_4vpph"),
&"Open": SubResource("Animation_5rj7k"),
&"RESET": SubResource("Animation_1ux4u")
}

[node name="PerksMenu" type="Node2D"]

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_1pbao")
}

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="Control" type="Control" parent="CanvasLayer"]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -21.0
offset_right = 20.0
offset_bottom = 19.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Sprite2D" parent="CanvasLayer/Control"]
position = Vector2(-6.99998, 31.5)
texture = ExtResource("1_0dex4")

[node name="ScrollContainer" type="ScrollContainer" parent="CanvasLayer/Control/Panel"]
offset_left = -86.0
offset_top = -86.0
offset_right = 122.0
offset_bottom = 117.0
scale = Vector2(0.835, 0.835)

[node name="HBoxContainer" type="VBoxContainer" parent="CanvasLayer/Control/Panel/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="ItemList1" type="ItemList" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer"]
custom_minimum_size = Vector2(300, 30.36)
layout_mode = 2

[node name="Button9" type="Button" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer/ItemList1"]
layout_mode = 0
offset_left = 37.1257
offset_top = 5.38924
offset_right = 55.1257
offset_bottom = 25.3892

[node name="ItemList2" type="ItemList" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer"]
custom_minimum_size = Vector2(300, 30.36)
layout_mode = 2

[node name="Button9" type="Button" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer/ItemList2"]
offset_left = 16.7665
offset_top = 6.11976
offset_right = 34.7665
offset_bottom = 26.1198

[node name="Button10" type="Button" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer/ItemList2"]
offset_left = 50.2994
offset_top = 4.92215
offset_right = 68.2994
offset_bottom = 24.9222

[node name="ItemList3" type="ItemList" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer"]
custom_minimum_size = Vector2(300, 30.36)
layout_mode = 2

[node name="Button9" type="Button" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer/ItemList3"]
offset_left = 16.7665
offset_top = 5.65267
offset_right = 34.7665
offset_bottom = 25.6527

[node name="Button10" type="Button" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer/ItemList3"]
offset_left = 51.497
offset_top = 4.45507
offset_right = 69.497
offset_bottom = 24.4551

[node name="ItemList4" type="ItemList" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer"]
custom_minimum_size = Vector2(300, 30.36)
layout_mode = 2

[node name="Button9" type="Button" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer/ItemList4"]
offset_left = 8.38323
offset_top = 3.98806
offset_right = 26.3832
offset_bottom = 23.9881

[node name="Button10" type="Button" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer/ItemList4"]
offset_left = 61.0778
offset_top = 3.98806
offset_right = 79.0778
offset_bottom = 23.9881

[node name="Button11" type="Button" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer/ItemList4"]
offset_left = 33.5329
offset_top = 3.98806
offset_right = 51.5329
offset_bottom = 23.9881

[node name="ItemList5" type="ItemList" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer"]
custom_minimum_size = Vector2(300, 30.36)
layout_mode = 2

[node name="Button9" type="Button" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer/ItemList5"]
offset_left = 8.38323
offset_top = 4.71858
offset_right = 26.3832
offset_bottom = 24.7186

[node name="ItemList6" type="ItemList" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer"]
custom_minimum_size = Vector2(300, 30.36)
layout_mode = 2

[node name="Button9" type="Button" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer/ItemList6"]
offset_left = 3.0
offset_top = 5.0
offset_right = 21.0
offset_bottom = 25.0

[node name="ItemList7" type="ItemList" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer"]
custom_minimum_size = Vector2(300, 30.36)
layout_mode = 2

[node name="Button9" type="Button" parent="CanvasLayer/Control/Panel/ScrollContainer/HBoxContainer/ItemList7"]
offset_left = 3.0
offset_top = 5.0
offset_right = 21.0
offset_bottom = 25.0

[node name="DescriptivePartPanel" type="Sprite2D" parent="CanvasLayer/Control/Panel"]
position = Vector2(137, -24.5)
texture = ExtResource("2_yh1ir")

[node name="ItemBackground" type="Sprite2D" parent="CanvasLayer/Control/Panel/DescriptivePartPanel"]
position = Vector2(0, -42)
texture = ExtResource("3_5rj7k")

[node name="ItemForeground" type="Sprite2D" parent="CanvasLayer/Control/Panel/DescriptivePartPanel/ItemBackground"]
texture = ExtResource("4_1ux4u")

[node name="SelectedBuffDescription" parent="CanvasLayer/Control/Panel/DescriptivePartPanel" instance=ExtResource("5_4vpph")]
offset_left = -44.0
offset_top = -25.0
offset_right = 117.0
offset_bottom = 81.0
scale = Vector2(0.545, 0.545)
horizontal_alignment = 0
vertical_alignment = 0

[node name="SelectedBuffButtonBackground" type="Sprite2D" parent="CanvasLayer/Control/Panel/DescriptivePartPanel"]
position = Vector2(1, 50)
scale = Vector2(0.729167, 0.5)
texture = ExtResource("6_1pbao")

[node name="SelectedBuffButtonBackgroundIcon" type="Sprite2D" parent="CanvasLayer/Control/Panel/DescriptivePartPanel"]
position = Vector2(4, 49.4)
scale = Vector2(0.489581, 0.5)
texture = ExtResource("4_1ux4u")

[node name="SelectedBuffButtonBackgroundLabel" parent="CanvasLayer/Control/Panel/DescriptivePartPanel" instance=ExtResource("5_4vpph")]
offset_left = -15.0
offset_top = 44.0
offset_right = 13.0
offset_bottom = 64.0
scale = Vector2(0.545, 0.545)
text = "5"
horizontal_alignment = 2

[node name="SelectedBuffButtonBackgroundLabelUnlocked" parent="CanvasLayer/Control/Panel/DescriptivePartPanel" instance=ExtResource("5_4vpph")]
offset_left = -15.0
offset_top = 43.0
offset_right = 110.0
offset_bottom = 94.0
scale = Vector2(0.255, 0.255)
text = "TEXT_UNLOCKED"

[node name="BackgroundPerksAmount" type="Sprite2D" parent="CanvasLayer/Control/Panel/DescriptivePartPanel"]
position = Vector2(-29.5, 74)
scale = Vector2(0.770834, 0.5625)
texture = ExtResource("6_1pbao")

[node name="PerksAmount" parent="CanvasLayer/Control/Panel/DescriptivePartPanel" instance=ExtResource("5_4vpph")]
offset_left = -35.0
offset_top = 67.0
offset_right = 1.0
offset_bottom = 89.0
scale = Vector2(0.59, 0.59)
text = "1000"

[node name="PerksIcon" type="Sprite2D" parent="CanvasLayer/Control/Panel/DescriptivePartPanel"]
position = Vector2(-39, 73)
scale = Vector2(0.59, 0.59)
texture = ExtResource("4_1ux4u")

[node name="UnlockButton" type="Button" parent="CanvasLayer/Control/Panel/DescriptivePartPanel"]
offset_left = -17.0
offset_top = 42.0
offset_right = 19.0
offset_bottom = 58.0

[node name="Close" type="Sprite2D" parent="CanvasLayer/Control/Panel"]
position = Vector2(191, -92.5)
texture = ExtResource("2_0dex4")

[node name="CloseButton" type="Button" parent="CanvasLayer/Control/Panel"]
offset_left = 181.0
offset_top = -104.5
offset_right = 201.0
offset_bottom = -82.5
