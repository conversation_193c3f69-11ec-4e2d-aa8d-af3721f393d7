using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

public partial class DungeonManager : Node2D
{
	[Export] public PackedScene DungeonVaseScene { get; set; }
	[Export] public PackedScene DungeonBatScene { get; set; }
	[Export] public PackedScene DungeonSkeletonScene { get; set; }

	private Dictionary<Vector2I, Node2D> _activeObjects = new();
	private Dictionary<Vector2I, Node2D> _activeEnemies = new();
	private List<DungeonObjectData> _initialObjectsConfig = new();
	private List<DungeonEnemyData> _initialEnemiesConfig = new();

	public override void _Ready()
	{
		// Define initial objects and enemies configuration
		SetupInitialObjectsConfig();
		SetupInitialEnemiesConfig();

		// Load existing objects and enemies or initialize for first time
		CallDeferred(nameof(LoadOrInitializeObjects));
		CallDeferred(nameof(LoadOrInitializeEnemies));
	}

	private void SetupInitialObjectsConfig()
	{
		// Define the initial dungeon objects and their positions
		_initialObjectsConfig = new List<DungeonObjectData>
		{
			new DungeonObjectData
			{
				TilePosition = new Vector2I(4,-8),
				ObjectType = DungeonObjectType.DungeonVase,
				MaxHealth = 10,
				CurrentHealth = 10,
				IsDestroyed = false
			},
			new DungeonObjectData
			{
				TilePosition = new Vector2I(5,-8),
				ObjectType = DungeonObjectType.DungeonVase,
				MaxHealth = 10,
				CurrentHealth = 10,
				IsDestroyed = false
			}
		};
	}

	private void SetupInitialEnemiesConfig()
	{
		// Define the initial dungeon enemies and their positions
		_initialEnemiesConfig = new List<DungeonEnemyData>
		{
			new DungeonEnemyData
			{
				SpawnTilePosition = new Vector2I(23, -20),
				EnemyType = DungeonEnemyType.DungeonBat,
				MaxHealth = 5,
				CurrentHealth = 5,
				IsKilled = false,
				RespawnOnEnter = true,
				Damage = 1
			},
			new DungeonEnemyData
			{
				SpawnTilePosition = new Vector2I(32, -2),
				EnemyType = DungeonEnemyType.DungeonBat,
				MaxHealth = 5,
				CurrentHealth = 5,
				IsKilled = false,
				RespawnOnEnter = true,
				Damage = 1
			},
			new DungeonEnemyData
			{
				SpawnTilePosition = new Vector2I(33, -3),
				EnemyType = DungeonEnemyType.DungeonBat,
				MaxHealth = 5,
				CurrentHealth = 5,
				IsKilled = false,
				RespawnOnEnter = true,
				Damage = 1
			},
			new DungeonEnemyData
			{
				SpawnTilePosition = new Vector2I(18, -5),
				EnemyType = DungeonEnemyType.DungeonBat,
				MaxHealth = 5,
				CurrentHealth = 5,
				IsKilled = false,
				RespawnOnEnter = true,
				Damage = 1
			},
			new DungeonEnemyData
			{
				SpawnTilePosition = new Vector2I(28, -25),
				EnemyType = DungeonEnemyType.DungeonBat,
				MaxHealth = 5,
				CurrentHealth = 5,
				IsKilled = false,
				RespawnOnEnter = true,
				Damage = 1
			},
			new DungeonEnemyData
			{
				SpawnTilePosition = new Vector2I(37, -24),
				EnemyType = DungeonEnemyType.DungeonBat,
				MaxHealth = 5,
				CurrentHealth = 5,
				IsKilled = false,
				RespawnOnEnter = true,
				Damage = 1
			},
			new DungeonEnemyData
			{
				SpawnTilePosition = new Vector2I(14, -5),
				EnemyType = DungeonEnemyType.DungeonSkeleton,
				MaxHealth = 5,
				CurrentHealth = 5,
				IsKilled = false,
				RespawnOnEnter = true,
				Damage = 1
			},
			new DungeonEnemyData
			{
				SpawnTilePosition = new Vector2I(15, -5),
				EnemyType = DungeonEnemyType.DungeonSkeleton,
				MaxHealth = 5,
				CurrentHealth = 5,
				IsKilled = false,
				RespawnOnEnter = true,
				Damage = 1
			}
		};
	}

	private void LoadOrInitializeObjects()
	{
		if (!GameSaveData.Instance.DungeonData.IsInitialized)
		{
			// First time initialization
			InitializeDungeonObjects();
		}
		else
		{
			// Load existing objects from save data
			LoadExistingObjects();
		}
	}

	private void InitializeDungeonObjects()
	{
		GD.Print("DungeonManager: Initializing dungeon objects for first time");

		// Copy initial config to save data
		GameSaveData.Instance.DungeonData.Objects = new List<DungeonObjectData>(_initialObjectsConfig);
		GameSaveData.Instance.DungeonData.IsInitialized = true;

		// Spawn all initial objects
		foreach (var objectData in _initialObjectsConfig)
		{
			SpawnDungeonObject(objectData);
		}

		GD.Print($"DungeonManager: Initialized {_initialObjectsConfig.Count} dungeon objects");
	}

	private void LoadExistingObjects()
	{
		GD.Print("DungeonManager: Loading existing dungeon objects from save data");

		var savedObjects = GameSaveData.Instance.DungeonData.Objects;
		int spawnedCount = 0;

		foreach (var objectData in savedObjects)
		{
			if (!objectData.IsDestroyed)
			{
				SpawnDungeonObject(objectData);
				spawnedCount++;
			}
		}

		GD.Print($"DungeonManager: Loaded {spawnedCount} existing dungeon objects");
	}

	private void LoadOrInitializeEnemies()
	{
		if (!GameSaveData.Instance.DungeonData.EnemiesInitialized)
		{
			// First time initialization
			InitializeDungeonEnemies();
		}
		else
		{
			// Load existing enemies from save data
			LoadExistingEnemies();
		}
	}

	private void InitializeDungeonEnemies()
	{
		GD.Print("DungeonManager: Initializing dungeon enemies for first time");

		// Copy initial config to save data
		GameSaveData.Instance.DungeonData.Enemies = new List<DungeonEnemyData>(_initialEnemiesConfig);
		GameSaveData.Instance.DungeonData.EnemiesInitialized = true;

		// Spawn all initial enemies
		foreach (var enemyData in _initialEnemiesConfig)
		{
			SpawnDungeonEnemy(enemyData);
		}

		GD.Print($"DungeonManager: Initialized {_initialEnemiesConfig.Count} dungeon enemies");
	}

	private void LoadExistingEnemies()
	{
		GD.Print("DungeonManager: Loading existing dungeon enemies from save data");

		var savedEnemies = GameSaveData.Instance.DungeonData.Enemies;
		int spawnedCount = 0;

		foreach (var enemyData in savedEnemies)
		{
			// Spawn enemy if it should respawn or if it's not killed
			if (enemyData.RespawnOnEnter || !enemyData.IsKilled)
			{
				SpawnDungeonEnemy(enemyData);
				spawnedCount++;
			}
		}

		GD.Print($"DungeonManager: Loaded {spawnedCount} existing dungeon enemies");
	}

	private void SpawnDungeonObject(DungeonObjectData objectData)
	{
		Node2D spawnedObject = null;

		switch (objectData.ObjectType)
		{
			case DungeonObjectType.DungeonVase:
				spawnedObject = SpawnDungeonVase(objectData);
				break;
			default:
				GD.PrintErr($"DungeonManager: Unknown object type {objectData.ObjectType}");
				return;
		}

		if (spawnedObject != null)
		{
			_activeObjects[objectData.TilePosition] = spawnedObject;
		}
	}

	private Node2D SpawnDungeonVase(DungeonObjectData objectData)
	{
		if (DungeonVaseScene == null)
		{
			GD.PrintErr("DungeonManager: DungeonVaseScene not set!");
			return null;
		}

		var vase = DungeonVaseScene.Instantiate<DungeonVase>();
		if (vase == null)
		{
			GD.PrintErr("DungeonManager: Failed to instantiate DungeonVase!");
			return null;
		}

		// Set position at center of tile (16x16 tiles)
		Vector2 worldPosition = new Vector2(
			objectData.TilePosition.X * 16 + 8,
			objectData.TilePosition.Y * 16 + 8
		);

		vase.GlobalPosition = worldPosition;
		vase.Initialize(objectData.TilePosition, objectData.CurrentHealth, objectData.MaxHealth);

		// Connect to destruction signal
		vase.DungeonVaseDestroyed += OnDungeonObjectDestroyed;

		// Add to scene
		GetParent().CallDeferred("add_child", vase);

		GD.Print($"DungeonManager: Spawned DungeonVase at tile {objectData.TilePosition} with {objectData.CurrentHealth}/{objectData.MaxHealth} HP");

		return vase;
	}

	private void SpawnDungeonEnemy(DungeonEnemyData enemyData)
	{
		Node2D spawnedEnemy = null;

		switch (enemyData.EnemyType)
		{
			case DungeonEnemyType.DungeonBat:
				spawnedEnemy = SpawnDungeonBat(enemyData);
				break;
			case DungeonEnemyType.DungeonSkeleton:
				spawnedEnemy = SpawnDungeonSkeleton(enemyData);
				break;
			default:
				GD.PrintErr($"DungeonManager: Unknown enemy type {enemyData.EnemyType}");
				return;
		}

		if (spawnedEnemy != null)
		{
			_activeEnemies[enemyData.SpawnTilePosition] = spawnedEnemy;
		}
	}

	private Node2D SpawnDungeonBat(DungeonEnemyData enemyData)
	{
		if (DungeonBatScene == null)
		{
			GD.PrintErr("DungeonManager: DungeonBatScene not set!");
			return null;
		}

		var bat = DungeonBatScene.Instantiate<DungeonBat>();
		if (bat == null)
		{
			GD.PrintErr("DungeonManager: Failed to instantiate DungeonBat!");
			return null;
		}

		bat.Initialize(enemyData.SpawnTilePosition, enemyData.CurrentHealth, enemyData.MaxHealth, enemyData.Damage);

		// Connect to death signal
		bat.DungeonBatKilled += OnDungeonEnemyKilled;

		// Add to scene
		GetParent().CallDeferred("add_child", bat);

		GD.Print($"DungeonManager: Spawned DungeonBat at tile {enemyData.SpawnTilePosition} with {enemyData.CurrentHealth}/{enemyData.MaxHealth} HP");

		return bat;
	}

	private Node2D SpawnDungeonSkeleton(DungeonEnemyData enemyData)
	{
		if (DungeonSkeletonScene == null)
		{
			GD.PrintErr("DungeonManager: DungeonSkeletonScene not set!");
			return null;
		}

		var skeleton = DungeonSkeletonScene.Instantiate<DungeonSkeleton>();
		if (skeleton == null)
		{
			GD.PrintErr("DungeonManager: Failed to instantiate DungeonSkeleton!");
			return null;
		}

		skeleton.Initialize(enemyData.SpawnTilePosition, enemyData.CurrentHealth, enemyData.MaxHealth, enemyData.Damage);

		// Connect to death signal
		skeleton.DungeonSkeletonKilled += OnDungeonEnemyKilled;

		// Add to scene
		GetParent().CallDeferred("add_child", skeleton);

		GD.Print($"DungeonManager: Spawned DungeonSkeleton at tile {enemyData.SpawnTilePosition} with {enemyData.CurrentHealth}/{enemyData.MaxHealth} HP");

		return skeleton;
	}

	private void OnDungeonObjectDestroyed(Vector2I tilePosition, DungeonObjectType objectType)
	{
		GD.Print($"DungeonManager: Object destroyed at {tilePosition}");

		// Remove from active objects
		if (_activeObjects.ContainsKey(tilePosition))
		{
			_activeObjects.Remove(tilePosition);
		}

		// Update save data - mark as destroyed
		var objectData = GameSaveData.Instance.DungeonData.Objects
			.FirstOrDefault(obj => obj.TilePosition == tilePosition && obj.ObjectType == objectType);

		if (objectData != null)
		{
			objectData.IsDestroyed = true;
			objectData.CurrentHealth = 0;
			GD.Print($"DungeonManager: Marked object at {tilePosition} as destroyed in save data");
		}
	}

	private void OnDungeonEnemyKilled(Vector2I spawnTilePosition)
	{
		GD.Print($"DungeonManager: Enemy killed at {spawnTilePosition}");

		// Remove from active enemies
		if (_activeEnemies.ContainsKey(spawnTilePosition))
		{
			_activeEnemies.Remove(spawnTilePosition);
		}

		// Update save data - mark as killed
		var enemyData = GameSaveData.Instance.DungeonData.Enemies
			.FirstOrDefault(enemy => enemy.SpawnTilePosition == spawnTilePosition);

		if (enemyData != null)
		{
			enemyData.IsKilled = true;
			enemyData.CurrentHealth = 0;
			GD.Print($"DungeonManager: Marked enemy at {spawnTilePosition} as killed in save data");
		}
	}

	public void UpdateObjectHealth(Vector2I tilePosition, int currentHealth)
	{
		// Update health in save data
		var objectData = GameSaveData.Instance.DungeonData.Objects
			.FirstOrDefault(obj => obj.TilePosition == tilePosition && !obj.IsDestroyed);

		if (objectData != null)
		{
			objectData.CurrentHealth = currentHealth;
			GD.Print($"DungeonManager: Updated object health at {tilePosition} to {currentHealth}");
		}
	}

	public void UpdateEnemyHealth(Vector2I spawnTilePosition, int currentHealth)
	{
		// Update health in save data
		var enemyData = GameSaveData.Instance.DungeonData.Enemies
			.FirstOrDefault(enemy => enemy.SpawnTilePosition == spawnTilePosition && !enemy.IsKilled);

		if (enemyData != null)
		{
			enemyData.CurrentHealth = currentHealth;
			GD.Print($"DungeonManager: Updated enemy health at {spawnTilePosition} to {currentHealth}");
		}
	}

	public override void _ExitTree()
	{
		// Clean up signal connections
		foreach (var kvp in _activeObjects)
		{
			if (kvp.Value is DungeonVase vase)
			{
				vase.DungeonVaseDestroyed -= OnDungeonObjectDestroyed;
			}
		}

		foreach (var kvp in _activeEnemies)
		{
			if (kvp.Value is DungeonBat bat)
			{
				bat.DungeonBatKilled -= OnDungeonEnemyKilled;
			}
		}

		base._ExitTree();
	}
}
