[gd_scene load_steps=16 format=3 uid="uid://dlyo1dgajqkf2"]

[ext_resource type="Texture2D" uid="uid://igcq7hp6jdf7" path="res://resources/solaria/UI/button2x2.png" id="1_vit7k"]
[ext_resource type="Texture2D" uid="uid://rfi41i25aveh" path="res://resources/solaria/UI/bagPackIcon.png" id="2_fnvjq"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="3_fnvjq"]
[ext_resource type="Script" uid="uid://cl3ocpvhfhw0l" path="res://scenes/UI/bagpack/BagpackButton.cs" id="4_zxcvb"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_nic1d"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3bumr"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_q8gd2"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_fpo0p"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_hv0ja"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_hiagn"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_545lc"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_q6aer"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_j410n"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_lapeh"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_lomtt"]

[node name="BagpackButton" type="Sprite2D"]
texture = ExtResource("1_vit7k")
script = ExtResource("4_zxcvb")

[node name="Icon" type="Sprite2D" parent="."]
texture = ExtResource("2_fnvjq")

[node name="Label" parent="." instance=ExtResource("3_fnvjq")]
offset_left = -13.0
offset_top = -14.0
offset_right = 2.0
offset_bottom = 3.0
scale = Vector2(0.64, 0.64)
text = "Q"

[node name="Button" type="Button" parent="."]
offset_left = -16.0
offset_top = -16.0
offset_right = 16.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_nic1d")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_3bumr")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_q8gd2")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_fpo0p")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_hv0ja")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_hiagn")
theme_override_styles/hover = SubResource("StyleBoxEmpty_545lc")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_q6aer")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_j410n")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_lapeh")
theme_override_styles/normal = SubResource("StyleBoxEmpty_lomtt")
