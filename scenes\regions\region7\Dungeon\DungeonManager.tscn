[gd_scene load_steps=5 format=3 uid="uid://c8vfv1kexku0y"]

[ext_resource type="Script" uid="uid://cx43fvf1do61g" path="res://scenes/regions/region7/Dungeon/DungeonManager.cs" id="1_dungeonmanager_script"]
[ext_resource type="PackedScene" uid="uid://b8vfv1kexku0y" path="res://scenes/regions/region7/Dungeon/DungeonVase.tscn" id="2_dungeonvase"]
[ext_resource type="PackedScene" uid="uid://dbr5otyrsi4yu" path="res://scenes/regions/region7/Dungeon/DungeonBat.tscn" id="3_dungeonbat"]
[ext_resource type="PackedScene" uid="uid://cyxx4pkh3bkby" path="res://scenes/regions/region7/Dungeon/DungeonSkeleton.tscn" id="4_dungeonskeleton"]

[node name="DungeonManager" type="Node2D"]
script = ExtResource("1_dungeonmanager_script")
DungeonVaseScene = ExtResource("2_dungeonvase")
DungeonBatScene = ExtResource("3_dungeonbat")
DungeonSkeletonScene = ExtResource("4_dungeonskeleton")
