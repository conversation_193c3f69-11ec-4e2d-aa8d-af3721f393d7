TASK-1:
We have DungeonEntrance scene and script. Scene is small (tscn), read it. When player is in range of PlayerDetector then you need to show KeyE prompt. When player clicks E then you need to change scene to Dungeon.tscn. If player leaves this range then you need to hide KeyE prompt. Look how entrance to home works - it needs to work similar here. Also, check on which layer player (homeplayer) listens in its player detector (it can be set in tscn and in script, verify) and make sure that in DungeonEntrance the player detector uses same layer. Use AreaEnter to detect when player is/is not in range. 

Then in Dungeon scene i included DungeonExit - it should work same way as DungeonEntrance - when player is in range of PlayerDetector then you need to show KeyE prompt. When player clicks E then you need to change scene to world.tscn. If player leaves this range then you need to hide KeyE prompt. Here also make sure that PlayerDetector in DungeonExit uses same layer as player detector (or in in DungeonEntrance).

TASK-2:
I have added Dungeon scene and the same scripts that are used in world i placed in the dungeon. Unfortunatelly, they look at reference world and can't find them. Make it works in dungeon. I mean look at InventoryMenu - it load nodes with path to world, but if in dungeon then should load from dungeon instead.
Additionally - look at game save data - they have position of player when game closes. But when we load game and player is in dungeon then he should be spawned in dungeon not world (if is in dungeon then save it's position in relation to dungeon not world). You need to add an enum to game save data with information in which scene player is (world, dungeon or house interior). actually we have it already it's SceneType.
When we load game from menu (we select game save) then we should load scene and player position based on this data. So if player was in dungeon - we should load dungeon scene and spawn player in dungeon. If player was in house interior then we should load house interior scene and spawn player there. If player was in world then we should load world scene and spawn player there.
Make sure this spawn system is properly working/loading player.
Also, when we change scene or load game, data in selectedtoolpanel should be preserved (especially in the dungeon and world, you can ignore house interior for now).