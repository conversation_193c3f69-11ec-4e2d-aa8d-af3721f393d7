using Godot;
using System.Linq;

public partial class DoorForSwitch : Node2D
{
	[Export] public string SwitchName { get; set; } = "DefaultSwitch";
	[Export] public Texture2D CustomTexture { get; set; }

	private AnimationPlayer _animationPlayer;
	private Sprite2D _sprite;
	private StaticBody2D _staticBody;
	private CollisionShape2D _collisionShape;
	private bool _isOpen = false;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_staticBody = GetNode<StaticBody2D>("StaticBody2D");
		_collisionShape = _staticBody.GetNode<CollisionShape2D>("CollisionShape2D");

		if (CustomTexture != null && _sprite != null)
		{
			_sprite.Texture = CustomTexture;
			GD.Print($"DoorForSwitch '{SwitchName}': Applied custom texture");
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwitchPressed += OnSwitchPressed;
		}

		LoadDoorState();
		GD.Print($"DoorForSwitch '{SwitchName}' initialized - door is {(_isOpen ? "open" : "closed")}");
	}

	private void OnSwitchPressed(string switchName)
	{
		if (switchName != SwitchName) return;

		if (!_isOpen)
		{
			OpenDoor();
		}
		else
		{
			CloseDoor();
		}
	}

	private void OpenDoor()
	{
		_isOpen = true;
		SaveDoorState();

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
			GD.Print($"DoorForSwitch '{SwitchName}': Playing Open animation");
		}
		else
		{
			SetDoorOpen();
		}
	}

	private void CloseDoor()
	{
		_isOpen = false;
		SaveDoorState();

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
			GD.Print($"DoorForSwitch '{SwitchName}': Playing Close animation");
		}
		else
		{
			SetDoorClosed();
		}
	}

	private void SetDoorOpen()
	{
		if (_collisionShape != null)
		{
			_collisionShape.Disabled = true;
		}
		GD.Print($"DoorForSwitch '{SwitchName}': Door opened - collision disabled");
	}

	private void SetDoorClosed()
	{
		if (_collisionShape != null)
		{
			_collisionShape.Disabled = false;
		}
		GD.Print($"DoorForSwitch '{SwitchName}': Door closed - collision enabled");
	}

	public bool IsOpen()
	{
		return _isOpen;
	}

	private void LoadDoorState()
	{
		var switchData = GameSaveData.Instance.DungeonData.Switches
			.FirstOrDefault(s => s.SwitchName == SwitchName);

		if (switchData != null)
		{
			_isOpen = switchData.IsOpen;
			if (_isOpen)
			{
				SetDoorOpen();
				if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
				{
					_animationPlayer.Play("Open");
				}
			}
			else
			{
				SetDoorClosed();
				if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
				{
					_animationPlayer.Play("Close");
				}
			}
		}
		else
		{
			SetDoorClosed();
		}
	}

	private void SaveDoorState()
	{
		var switchData = GameSaveData.Instance.DungeonData.Switches
			.FirstOrDefault(s => s.SwitchName == SwitchName);

		if (switchData != null)
		{
			switchData.IsOpen = _isOpen;
		}
		else
		{
			GameSaveData.Instance.DungeonData.Switches.Add(new DungeonSwitchData
			{
				SwitchName = SwitchName,
				IsOpen = _isOpen
			});
		}

		GD.Print($"DoorForSwitch '{SwitchName}': Saved state - IsOpen: {_isOpen}");
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwitchPressed -= OnSwitchPressed;
		}

		base._ExitTree();
	}
}
