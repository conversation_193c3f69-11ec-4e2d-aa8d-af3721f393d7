[gd_scene load_steps=9 format=3 uid="uid://borl2iygx76kb"]

[ext_resource type="Script" uid="uid://dyimr108xncb6" path="res://scenes/regions/region7/Dungeon/Quests/DoorForSwitch.cs" id="1_doorforswitch_script"]
[ext_resource type="Texture2D" uid="uid://cvtea8mnq6uj2" path="res://resources/solaria/crypt/doorBrown4.png" id="2_xm62t"]

[sub_resource type="Animation" id="Animation_xm62t"]
resource_name = "Close"
length = 0.6
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [6, 5, 4, 3, 2, 1, 0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("StaticBody2D/CollisionShape2D:disabled")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.6),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_clkeu"]
resource_name = "Hide"
length = 0.1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [6]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("StaticBody2D/CollisionShape2D:disabled")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_sq26m"]
resource_name = "Open"
length = 0.6
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5, 6]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("StaticBody2D/CollisionShape2D:disabled")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.6),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_fcy1n"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("StaticBody2D/CollisionShape2D:disabled")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_clkeu"]
_data = {
&"Close": SubResource("Animation_xm62t"),
&"Hide": SubResource("Animation_clkeu"),
&"Open": SubResource("Animation_sq26m"),
&"RESET": SubResource("Animation_fcy1n")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_2mo3b"]
size = Vector2(16, 8)

[node name="DoorForSwitch" type="Node2D"]
script = ExtResource("1_doorforswitch_script")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_clkeu")
}

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_xm62t")
hframes = 7

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 4)
shape = SubResource("RectangleShape2D_2mo3b")
disabled = true
