[gd_scene load_steps=21 format=3 uid="uid://c5s5c4147neyh"]

[ext_resource type="Texture2D" uid="uid://0o371ajdg6ya" path="res://resources/solaria/crypt/button_blue.png" id="1_a0qkp"]
[ext_resource type="Texture2D" uid="uid://bixabarverqmj" path="res://resources/solaria/crypt/button_green.png" id="2_mvfnd"]
[ext_resource type="Texture2D" uid="uid://rl6uvhqi802s" path="res://resources/solaria/crypt/button_orange.png" id="3_k1spw"]
[ext_resource type="Texture2D" uid="uid://dwunp5r7otmau" path="res://resources/solaria/crypt/button_red.png" id="4_6do3h"]
[ext_resource type="Texture2D" uid="uid://gibgfi30e4l1" path="res://resources/solaria/crypt/button_white.png" id="5_o8amp"]
[ext_resource type="Texture2D" uid="uid://bf2que6iqc82o" path="res://resources/solaria/crypt/doorGreen.png" id="6_k1spw"]
[ext_resource type="Script" uid="uid://dfowf50wauld5" path="res://scenes/regions/region7/Dungeon/Quests/DoorSwitchQuestBig.cs" id="7_script"]

[sub_resource type="Animation" id="Animation_ynsrx"]
resource_name = "Off"
length = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="Animation" id="Animation_to5p7"]
resource_name = "On"
length = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [1]
}

[sub_resource type="Animation" id="Animation_qajxr"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_mvfnd"]
_data = {
&"Off": SubResource("Animation_ynsrx"),
&"On": SubResource("Animation_to5p7"),
&"RESET": SubResource("Animation_qajxr")
}

[sub_resource type="CircleShape2D" id="CircleShape2D_mvfnd"]
radius = 6.0

[sub_resource type="CircleShape2D" id="CircleShape2D_k1spw"]
radius = 6.08276

[sub_resource type="CircleShape2D" id="CircleShape2D_6do3h"]
radius = 6.0

[sub_resource type="CircleShape2D" id="CircleShape2D_o8amp"]
radius = 6.08276

[sub_resource type="CircleShape2D" id="CircleShape2D_l5bdr"]
radius = 6.08276

[sub_resource type="Animation" id="Animation_6do3h"]
resource_name = "Open"
length = 0.6
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5, 6]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Area2D/CollisionShape2D:disabled")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.5),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_o8amp"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Area2D/CollisionShape2D:disabled")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_l5bdr"]
_data = {
&"Open": SubResource("Animation_6do3h"),
&"RESET": SubResource("Animation_o8amp")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_k1spw"]
size = Vector2(32, 2)

[node name="DoorSwitchQuestBig" type="Node2D"]
y_sort_enabled = true
script = ExtResource("7_script")

[node name="Switch1" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(-48, -48)
texture = ExtResource("1_a0qkp")
offset = Vector2(0, 48)
hframes = 2

[node name="AnimationPlayer" type="AnimationPlayer" parent="Switch1"]
libraries = {
&"": SubResource("AnimationLibrary_mvfnd")
}

[node name="Area2D" type="Area2D" parent="Switch1"]
position = Vector2(0, 48)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Switch1/Area2D"]
shape = SubResource("CircleShape2D_mvfnd")

[node name="Switch2" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(-24, -48)
texture = ExtResource("2_mvfnd")
offset = Vector2(0, 48)
hframes = 2

[node name="AnimationPlayer" type="AnimationPlayer" parent="Switch2"]
libraries = {
&"": SubResource("AnimationLibrary_mvfnd")
}

[node name="Area2D" type="Area2D" parent="Switch2"]
position = Vector2(0, 48)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Switch2/Area2D"]
shape = SubResource("CircleShape2D_k1spw")

[node name="Switch3" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -48)
texture = ExtResource("3_k1spw")
offset = Vector2(0, 48)
hframes = 2

[node name="AnimationPlayer" type="AnimationPlayer" parent="Switch3"]
libraries = {
&"": SubResource("AnimationLibrary_mvfnd")
}

[node name="Area2D" type="Area2D" parent="Switch3"]
position = Vector2(0, 48)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Switch3/Area2D"]
shape = SubResource("CircleShape2D_6do3h")

[node name="Switch4" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(24, -48)
texture = ExtResource("4_6do3h")
offset = Vector2(0, 48)
hframes = 2

[node name="AnimationPlayer" type="AnimationPlayer" parent="Switch4"]
libraries = {
&"": SubResource("AnimationLibrary_mvfnd")
}

[node name="Area2D" type="Area2D" parent="Switch4"]
position = Vector2(0, 48)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Switch4/Area2D"]
shape = SubResource("CircleShape2D_o8amp")

[node name="Switch5" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(48, -48)
texture = ExtResource("5_o8amp")
offset = Vector2(0, 48)
hframes = 2

[node name="AnimationPlayer" type="AnimationPlayer" parent="Switch5"]
libraries = {
&"": SubResource("AnimationLibrary_mvfnd")
}

[node name="Area2D" type="Area2D" parent="Switch5"]
position = Vector2(0, 48)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Switch5/Area2D"]
shape = SubResource("CircleShape2D_l5bdr")

[node name="Door" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, 65)
texture = ExtResource("6_k1spw")
hframes = 7

[node name="AnimationPlayer" type="AnimationPlayer" parent="Door"]
libraries = {
&"": SubResource("AnimationLibrary_l5bdr")
}

[node name="Area2D" type="Area2D" parent="Door"]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Door/Area2D"]
position = Vector2(0, 7)
shape = SubResource("RectangleShape2D_k1spw")
disabled = true
