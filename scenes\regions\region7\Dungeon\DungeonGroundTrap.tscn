[gd_scene load_steps=8 format=3 uid="uid://ct2xtf4ktmakj"]

[ext_resource type="Texture2D" uid="uid://dcki6y5j072ep" path="res://resources/solaria/crypt/trap_1.png" id="1_aghlt"]
[ext_resource type="Script" uid="uid://cm7yn7n4mrnqc" path="res://scenes/regions/region7/Dungeon/DungeonGroundTrap.cs" id="1_dungeongroundtrap_script"]

[sub_resource type="Animation" id="Animation_aghlt"]
resource_name = "Hide"
length = 0.3
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [2, 1, 0]
}

[sub_resource type="Animation" id="Animation_v5m1n"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="Animation" id="Animation_2w658"]
resource_name = "Show"
length = 0.3
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [0, 1, 2]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_sklbi"]
_data = {
&"Hide": SubResource("Animation_aghlt"),
&"RESET": SubResource("Animation_v5m1n"),
&"Show": SubResource("Animation_2w658")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_aghlt"]
size = Vector2(11, 11)

[node name="DungeonGroundTrap" type="Node2D"]
script = ExtResource("1_dungeongroundtrap_script")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("1_aghlt")
hframes = 4

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_sklbi")
}

[node name="Area2D" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
position = Vector2(-0.5, -0.5)
shape = SubResource("RectangleShape2D_aghlt")
