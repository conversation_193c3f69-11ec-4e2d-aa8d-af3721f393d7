I want to create other enemy in the dungeon. This enemy will be a skeleton. Duplicate 
1:1 how bat works (all logic like attacking player etc) but with following differences:

1) <PERSON><PERSON><PERSON> will attack with sword (included in animations) - he will not "move in player direction" when taking attack but rather stand in place and attack with sword - similar like meleegoblin except meleegoblin has different logic for everything, and here it has to be simple like bat.
2) Detect enemy attacking dungeon player the same way dungeon player attacking enemy with sword bun in opposite direction
3) we will have animations: idle (each direction), move (each direction) and attack (each direction) and got hit (each direction).

I will set animation of enemy and I will set sprite of skeleton. Actually, sprite is res://resources/solaria/enemies/Skeleton 01.png you can use it.

Make sure project builds.
Verify TWICE! if everything is as should - implemented correctly. I will lose money if it wont work.
Then verify once again if everything is implemented as expected. I will set animations.